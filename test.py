#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python数据类型学习示例
包含Python中所有主要的数据类型和使用方法
"""

print("=" * 50)
print("Python数据类型学习示例")
print("=" * 50)

# 1. 数字类型 (Numbers)
print("\n1. 数字类型 (Numbers)")
print("-" * 30)

# 整数 (int)
integer_num = 42
negative_int = -10
big_int = 123456789012345678901234567890  # Python支持任意大的整数

print(f"整数: {integer_num}, 类型: {type(integer_num)}")
print(f"负整数: {negative_int}")
print(f"大整数: {big_int}")

# 浮点数 (float)
float_num = 3.14159
scientific_notation = 1.23e-4  # 科学计数法

print(f"浮点数: {float_num}, 类型: {type(float_num)}")
print(f"科学计数法: {scientific_notation}")

# 复数 (complex)
complex_num = 3 + 4j
print(f"复数: {complex_num}, 类型: {type(complex_num)}")
print(f"复数的实部: {complex_num.real}, 虚部: {complex_num.imag}")

# 2. 字符串类型 (String)
print("\n2. 字符串类型 (String)")
print("-" * 30)

# 不同的字符串定义方式
single_quote = '单引号字符串'
double_quote = "双引号字符串"
triple_quote = """三引号字符串
可以跨行"""

print(f"单引号: {single_quote}, 类型: {type(single_quote)}")
print(f"双引号: {double_quote}")
print(f"三引号: {repr(triple_quote)}")

# 字符串操作
name = "Python"
version = "3.9"
greeting = f"Hello, {name} {version}!"  # f-string格式化

print(f"字符串拼接: {greeting}")
print(f"字符串长度: {len(greeting)}")
print(f"转大写: {greeting.upper()}")
print(f"字符串切片: {greeting[0:5]}")

# 3. 布尔类型 (Boolean)
print("\n3. 布尔类型 (Boolean)")
print("-" * 30)

is_true = True
is_false = False

print(f"True值: {is_true}, 类型: {type(is_true)}")
print(f"False值: {is_false}")
print(f"布尔运算: {is_true and is_false}")
print(f"布尔运算: {is_true or is_false}")
print(f"布尔运算: {not is_false}")

# 4. 列表类型 (List)
print("\n4. 列表类型 (List)")
print("-" * 30)

# 创建列表
numbers = [1, 2, 3, 4, 5]
mixed_list = [1, "hello", 3.14, True, [1, 2, 3]]
empty_list = []

print(f"数字列表: {numbers}, 类型: {type(numbers)}")
print(f"混合列表: {mixed_list}")
print(f"空列表: {empty_list}")

# 列表操作
numbers.append(6)  # 添加元素
print(f"添加元素后: {numbers}")
print(f"列表长度: {len(numbers)}")
print(f"第一个元素: {numbers[0]}")
print(f"最后一个元素: {numbers[-1]}")
print(f"切片操作: {numbers[1:4]}")

# 5. 元组类型 (Tuple)
print("\n5. 元组类型 (Tuple)")
print("-" * 30)

# 创建元组
coordinates = (10, 20)
single_tuple = (42,)  # 单元素元组需要逗号
mixed_tuple = (1, "hello", 3.14)

print(f"坐标元组: {coordinates}, 类型: {type(coordinates)}")
print(f"单元素元组: {single_tuple}")
print(f"混合元组: {mixed_tuple}")

# 元组操作（不可变）
print(f"元组长度: {len(coordinates)}")
print(f"第一个元素: {coordinates[0]}")
# coordinates[0] = 30  # 这会报错，因为元组不可变

# 6. 字典类型 (Dictionary)
print("\n6. 字典类型 (Dictionary)")
print("-" * 30)

# 创建字典
person = {
    "name": "张三",
    "age": 25,
    "city": "北京",
    "is_student": False
}

empty_dict = {}

print(f"人员信息: {person}, 类型: {type(person)}")
print(f"空字典: {empty_dict}")

# 字典操作
print(f"姓名: {person['name']}")
print(f"年龄: {person.get('age', '未知')}")
person["email"] = "<EMAIL>"  # 添加新键值对
print(f"添加邮箱后: {person}")
print(f"所有键: {list(person.keys())}")
print(f"所有值: {list(person.values())}")

# 7. 集合类型 (Set)
print("\n7. 集合类型 (Set)")
print("-" * 30)

# 创建集合
fruits = {"apple", "banana", "orange", "apple"}  # 重复元素会被去除
numbers_set = set([1, 2, 3, 4, 5, 3, 2])
empty_set = set()  # 注意：{}创建的是空字典，不是空集合

print(f"水果集合: {fruits}, 类型: {type(fruits)}")
print(f"数字集合: {numbers_set}")
print(f"空集合: {empty_set}")

# 集合操作
fruits.add("grape")  # 添加元素
print(f"添加葡萄后: {fruits}")

other_fruits = {"banana", "kiwi", "mango"}
print(f"并集: {fruits | other_fruits}")
print(f"交集: {fruits & other_fruits}")
print(f"差集: {fruits - other_fruits}")

# 8. None类型
print("\n8. None类型")
print("-" * 30)

nothing = None
print(f"None值: {nothing}, 类型: {type(nothing)}")
print(f"是否为None: {nothing is None}")

# 9. 类型转换示例
print("\n9. 类型转换示例")
print("-" * 30)

# 数字转换
str_num = "123"
int_from_str = int(str_num)
float_from_str = float(str_num)

print(f"字符串转整数: '{str_num}' -> {int_from_str}")
print(f"字符串转浮点数: '{str_num}' -> {float_from_str}")

# 字符串转换
num = 456
str_from_int = str(num)
print(f"整数转字符串: {num} -> '{str_from_int}'")

# 列表和元组转换
list_data = [1, 2, 3]
tuple_from_list = tuple(list_data)
list_from_tuple = list(tuple_from_list)

print(f"列表转元组: {list_data} -> {tuple_from_list}")
print(f"元组转列表: {tuple_from_list} -> {list_from_tuple}")

# 10. 类型检查
print("\n10. 类型检查")
print("-" * 30)

test_var = "Hello World"
print(f"变量值: {test_var}")
print(f"类型: {type(test_var)}")
print(f"是否为字符串: {isinstance(test_var, str)}")
print(f"是否为整数: {isinstance(test_var, int)}")
print(f"是否为字符串或整数: {isinstance(test_var, (str, int))}")

print("\n" + "=" * 50)
print("Python数据类型学习完成！")
print("=" * 50)